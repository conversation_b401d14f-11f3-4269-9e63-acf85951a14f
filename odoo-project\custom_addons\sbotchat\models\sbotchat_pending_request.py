# -*- coding: utf-8 -*-

from odoo import models, fields, api
import logging
import json
from datetime import datetime, timedelta

_logger = logging.getLogger(__name__)

class SbotchatPendingRequest(models.Model):
    _name = 'sbotchat.pending.request'
    _description = 'Yêu cầu chat đang chờ xử lý'
    _order = 'create_date desc'

    conversation_id = fields.Many2one('sbotchat.conversation', string='Cuộc trò chuyện', required=True, ondelete='cascade')
    user_id = fields.Many2one('res.users', string='Người dùng', required=True)
    user_message = fields.Text('Tin nhắn người dùng', required=True)
    status = fields.Selection([
        ('pending', 'Đang chờ'),
        ('processing', 'Đang xử lý'),
        ('completed', 'Hoàn thành'),
        ('failed', 'Thất bại'),
        ('cancelled', 'Đã hủy')
    ], string='Trạng thái', default='pending', required=True)
    
    config_data = fields.Text('Dữ liệu cấu hình', help='JSON config cho request')
    request_time = fields.Datetime('Thời gian yêu cầu', default=fields.Datetime.now)
    processing_start = fields.Datetime('Bắt đầu xử lý')
    processing_end = fields.Datetime('Kết thúc xử lý')
    error_message = fields.Text('Thông báo lỗi')
    
    # Response data
    assistant_response = fields.Text('Phản hồi trợ lý')
    model_used = fields.Char('Model sử dụng')
    tokens_used = fields.Integer('Token sử dụng', default=0)
    response_time = fields.Float('Thời gian phản hồi (giây)', default=0.0)
    
    # Notification flags
    user_notified = fields.Boolean('Đã thông báo người dùng', default=False)
    notification_sent = fields.Datetime('Thời gian gửi thông báo')

    @api.model
    def create_pending_request(self, conversation_id, user_message, config_data=None):
        """Tạo request pending mới"""
        try:
            vals = {
                'conversation_id': conversation_id,
                'user_id': self.env.user.id,
                'user_message': user_message,
                'status': 'pending',
                'config_data': json.dumps(config_data) if config_data else None
            }
            
            # Use sudo() to ensure creation succeeds
            request = self.sudo().create(vals)
            _logger.info(f"Tạo pending request {request.id} cho conversation {conversation_id}")
            
            return request
            
        except Exception as e:
            _logger.error(f"Lỗi khi tạo pending request: {str(e)}")
            raise

    def mark_processing(self):
        """Đánh dấu request đang được xử lý"""
        self.write({
            'status': 'processing',
            'processing_start': fields.Datetime.now()
        })

    def mark_completed(self, assistant_response, model_used=None, tokens_used=0, response_time=0.0):
        """Đánh dấu request hoàn thành"""
        self.write({
            'status': 'completed',
            'processing_end': fields.Datetime.now(),
            'assistant_response': assistant_response,
            'model_used': model_used,
            'tokens_used': tokens_used,
            'response_time': response_time
        })
        
        # Tạo message trong conversation
        self._create_conversation_messages()
        
        # Gửi thông báo đến user
        self._notify_user_completion()

    def mark_failed(self, error_message):
        """Đánh dấu request thất bại"""
        self.write({
            'status': 'failed',
            'processing_end': fields.Datetime.now(),
            'error_message': error_message
        })
        
        # Gửi thông báo lỗi đến user
        self._notify_user_error()

    def _create_conversation_messages(self):
        """Tạo tin nhắn trong conversation từ pending request"""
        try:
            message_env = self.env['sbotchat.message']
            
            # Tạo user message
            message_env.create({
                'conversation_id': self.conversation_id.id,
                'role': 'user',
                'content': self.user_message
            })
            
            # Tạo assistant response
            if self.assistant_response:
                message_env.create({
                    'conversation_id': self.conversation_id.id,
                    'role': 'assistant',
                    'content': self.assistant_response,
                    'model_used': self.model_used,
                    'tokens_used': self.tokens_used,
                    'response_time': self.response_time
                })
            
            _logger.info(f"Đã tạo tin nhắn cho conversation {self.conversation_id.id}")
            
        except Exception as e:
            _logger.error(f"Lỗi khi tạo tin nhắn: {str(e)}")

    def _notify_user_completion(self):
        """Thông báo cho user khi request hoàn thành"""
        try:
            # Tạo notification trong bus
            self.env['bus.bus']._sendone(
                f'sbotchat_channel_{self.user_id.id}',
                'sbotchat_response_ready',
                {
                    'type': 'response_ready',
                    'conversation_id': self.conversation_id.id,
                    'request_id': self.id,
                    'title': f'Phản hồi chat đã sẵn sàng',
                    'message': f'SBot đã trả lời trong cuộc trò chuyện "{self.conversation_id.title}"',
                    'conversation_title': self.conversation_id.title
                }
            )
            
            self.write({
                'user_notified': True,
                'notification_sent': fields.Datetime.now()
            })
            
            _logger.info(f"Đã gửi thông báo hoàn thành cho user {self.user_id.name}")
            
        except Exception as e:
            _logger.error(f"Lỗi khi gửi thông báo: {str(e)}")

    def _notify_user_error(self):
        """Thông báo cho user khi có lỗi"""
        try:
            self.env['bus.bus']._sendone(
                f'sbotchat_channel_{self.user_id.id}',
                'sbotchat_response_error',
                {
                    'type': 'response_error',
                    'conversation_id': self.conversation_id.id,
                    'request_id': self.id,
                    'title': 'Lỗi xử lý chat',
                    'message': f'Có lỗi khi xử lý tin nhắn: {self.error_message}',
                    'error': self.error_message
                }
            )
            
            self.write({
                'user_notified': True,
                'notification_sent': fields.Datetime.now()
            })
            
            _logger.info(f"Đã gửi thông báo lỗi cho user {self.user_id.name}")
            
        except Exception as e:
            _logger.error(f"Lỗi khi gửi thông báo lỗi: {str(e)}")

    @api.model
    def get_user_pending_requests(self, user_id=None):
        """Lấy danh sách request pending của user"""
        if not user_id:
            user_id = self.env.user.id
            
        # Use sudo() to ensure access
        return self.sudo().search([
            ('user_id', '=', user_id),
            ('status', 'in', ['pending', 'processing'])
        ])

    @api.model
    def process_pending_requests(self, limit=10):
        """Xử lý các request pending (được gọi bởi cron job)"""
        try:
            pending_requests = self.search([
                ('status', '=', 'pending')
            ], limit=limit, order='create_date asc')
            
            for request in pending_requests:
                try:
                    self._process_single_request(request)
                except Exception as e:
                    _logger.error(f"Lỗi xử lý request {request.id}: {str(e)}")
                    request.mark_failed(str(e))
                    
        except Exception as e:
            _logger.error(f"Lỗi trong process_pending_requests: {str(e)}")

    def _process_single_request(self, request):
        """Xử lý một request đơn lẻ"""
        try:
            request.mark_processing()
            
            # Parse config data
            config_data = {}
            if request.config_data:
                config_data = json.loads(request.config_data)
            
            
            
            # Lấy config từ database
            config_model = self.env['sbotchat.config']
            config = config_model.search([('user_id', '=', request.user_id.id)], limit=1)
            if not config:
                raise Exception("Không tìm thấy cấu hình SBot Chat")
            
    
            response_data = {
                'response': 'Agent processing has been removed',
                'model_used': 'none',
                'tokens_used': 0,
                'response_time': 0.0
            }
            
            # Đánh dấu hoàn thành
            request.mark_completed(
                assistant_response=response_data.get('response', ''),
                model_used=response_data.get('model_used', ''),
                tokens_used=response_data.get('tokens_used', 0),
                response_time=response_data.get('response_time', 0.0)
            )
            
        except Exception as e:
            _logger.error(f"Lỗi xử lý request {request.id}: {str(e)}")
            raise

    @api.model
    def cleanup_old_requests(self, days=7):
        """Dọn dẹp các request cũ"""
        try:
            cutoff_date = fields.Datetime.now() - timedelta(days=days)
            old_requests = self.search([
                ('create_date', '<', cutoff_date),
                ('status', 'in', ['completed', 'failed', 'cancelled'])
            ])
            
            count = len(old_requests)
            if count > 0:
                old_requests.unlink()
                _logger.info(f"Đã xóa {count} pending request cũ")
                
        except Exception as e:
            _logger.error(f"Lỗi khi dọn dẹp request cũ: {str(e)}") 