/** @odoo-module **/

import { Component, useState, onMounted, onWillUnmount } from "@odoo/owl";
import { registry } from "@web/core/registry";
import { useService } from "@web/core/utils/hooks";

/**
 * SBot Chat Global Floating Button
 * Hiển thị button floating với chữ SBOT để mở chat interface
 * Có thể hiển thị indicator cho pending requests và notifications
 */
class SbotchatFloatingButton extends Component {
  static template = "sbotchat.FloatingButton";

  setup() {
    this.action = useService("action");
    this.rpc = useService("rpc");
    this.bus = useService("bus_service");

    // State for notifications and pending requests
    this.state = useState({
      pendingCount: 0,
      hasNotification: false,
      notificationMessage: "",
      isBlinking: false,
    });

    onMounted(() => {
      this.initializeFloatingButton();
      this.setupNotificationListener();
      this.checkPendingRequests();
    });

    onWillUnmount(() => {
      this.cleanup();
    });
  }

  /**
   * Initialize floating button
   */
  initializeFloatingButton() {
    console.log("🎯 SBot Floating Button đã được khởi tạo");

    // Start periodic check for pending requests
    this.pendingCheckInterval = setInterval(() => {
      this.checkPendingRequests();
    }, 30000); // Check every 30 seconds
  }

  /**
   * Setup notification listener for bus messages
   */
  setupNotificationListener() {
    try {
      // Request notification permission
      this.requestNotificationPermission();

      // Subscribe to user-specific channel
      const userId = odoo.session_info?.uid || 1;
      this.bus.subscribe(`sbotchat_channel_${userId}`, (message) => {
        this.handleBusMessage(message);
      });
    } catch (error) {
      console.warn("Không thể setup notification listener:", error);
    }
  }

  /**
   * Request browser notification permission
   */
  async requestNotificationPermission() {
    try {
      if ("Notification" in window) {
        if (Notification.permission === "default") {
          const permission = await Notification.requestPermission();
          console.log("Notification permission:", permission);
        }
      }
    } catch (error) {
      console.warn("Không thể yêu cầu quyền notification:", error);
    }
  }

  /**
   * Handle bus messages from backend
   */
  handleBusMessage(message) {
    try {
      const { type, title, message: msg, conversation_title } = message;

      if (type === "response_ready") {
        this.showResponseReadyNotification(title, msg, conversation_title);
        this.state.pendingCount = Math.max(0, this.state.pendingCount - 1);
        this.updateFloatingButton();
      } else if (type === "response_error") {
        this.showErrorNotification(title, msg);
        this.state.pendingCount = Math.max(0, this.state.pendingCount - 1);
        this.updateFloatingButton();
      }
    } catch (error) {
      console.error("Lỗi xử lý bus message:", error);
    }
  }

  /**
   * Show notification when response is ready
   */
  showResponseReadyNotification(title, message, conversationTitle) {
    this.state.hasNotification = true;
    this.state.notificationMessage = `${conversationTitle}: Phản hồi đã sẵn sàng`;
    this.state.isBlinking = true;

    // Show system notification if supported
    if ("Notification" in window && Notification.permission === "granted") {
      new Notification(title, {
        body: message,
        icon: "/sbotchat/static/src/img/sbot_icon.png",
      });
    }

    // Stop blinking after 5 seconds
    setTimeout(() => {
      this.state.isBlinking = false;
    }, 5000);

    this.updateFloatingButton();
  }

  /**
   * Show error notification
   */
  showErrorNotification(title, message) {
    this.showNotification(message, "danger");
    this.updateFloatingButton();
  }

  /**
   * Check for pending requests
   */
  async checkPendingRequests() {
    try {
      const result = await this.rpc("/web/dataset/call_kw", {
        model: "sbotchat.pending.request",
        method: "get_user_pending_requests",
        args: [],
        kwargs: {},
      });

      if (result && Array.isArray(result)) {
        this.state.pendingCount = result.length;
        this.updateFloatingButton();
      }
    } catch (error) {
      // Silent fail for pending check
      console.debug("Không thể kiểm tra pending requests:", error);
    }
  }

  /**
   * Update floating button appearance
   */
  updateFloatingButton() {
    const floatingBtn = document.querySelector(".sbotchat-global-floating-btn");
    if (!floatingBtn) return;

    // Update pending count indicator
    let indicator = floatingBtn.querySelector(".pending-indicator");
    if (this.state.pendingCount > 0) {
      if (!indicator) {
        indicator = document.createElement("div");
        indicator.className = "pending-indicator";
        floatingBtn.appendChild(indicator);
      }
      indicator.textContent = this.state.pendingCount;
      indicator.style.display = "block";
    } else if (indicator) {
      indicator.style.display = "none";
    }

    // Update notification indicator
    let notifIndicator = floatingBtn.querySelector(".notification-indicator");
    if (this.state.hasNotification) {
      if (!notifIndicator) {
        notifIndicator = document.createElement("div");
        notifIndicator.className = "notification-indicator";
        floatingBtn.appendChild(notifIndicator);
      }
      notifIndicator.style.display = "block";
    } else if (notifIndicator) {
      notifIndicator.style.display = "none";
    }

    // Add blinking effect
    if (this.state.isBlinking) {
      floatingBtn.classList.add("blinking");
    } else {
      floatingBtn.classList.remove("blinking");
    }
  }

  /**
   * Cleanup intervals and listeners
   */
  cleanup() {
    if (this.pendingCheckInterval) {
      clearInterval(this.pendingCheckInterval);
    }
  }

  /**
   * Open chat interface
   */
  openChat() {
    try {
      console.log("🚀 Mở SBot Chat từ floating button...");

      // Clear notification state when opening chat
      this.state.hasNotification = false;
      this.state.notificationMessage = "";
      this.state.isBlinking = false;
      this.updateFloatingButton();

      // Hide floating button
      this.hideFloatingButton();

      // Open chat interface
      this.action.doAction({
        type: "ir.actions.client",
        tag: "sbotchat.interface",
        name: "SBot Chat",
        target: "fullscreen",
      });
    } catch (error) {
      console.error("❌ Không thể mở SBot Chat:", error);
      this.showNotification("Không thể mở giao diện chat", "danger");
    }
  }

  /**
   * Hide floating button
   */
  hideFloatingButton() {
    const floatingBtn = document.querySelector(".sbotchat-global-floating-btn");
    if (floatingBtn) {
      floatingBtn.style.display = "none";
    }
  }

  /**
   * Show floating button
   */
  showFloatingButton() {
    const floatingBtn = document.querySelector(".sbotchat-global-floating-btn");
    if (floatingBtn) {
      floatingBtn.style.display = "flex";
    }
  }

  /**
   * Show notification
   */
  showNotification(message, type = "info") {
    // Create custom notification
    const notification = document.createElement("div");
    notification.className = `custom-notification notification-${type}`;
    notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem 1.5rem;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 0.75rem;
            color: #0f172a;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            z-index: 9999;
            transform: translateX(100%);
            transition: all 0.3s ease;
            max-width: 300px;
            word-wrap: break-word;
        `;

    notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 0.5rem;">
                <i class="fa fa-${
                  type === "success"
                    ? "check"
                    : type === "danger"
                    ? "times"
                    : "info"
                }-circle"></i>
                <span>${message}</span>
            </div>
        `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
      notification.style.transform = "translateX(0)";
    }, 10);

    // Auto remove
    setTimeout(() => {
      notification.style.transform = "translateX(100%)";
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, 4000);
  }
}

/**
 * Global Floating Button Manager
 * Quản lý việc hiển thị floating button trên toàn bộ Odoo
 */
class SbotchatGlobalManager {
  constructor() {
    this.floatingButton = null;
    this.isInitialized = false;
  }

  /**
   * Initialize global floating button
   */
  init() {
    if (this.isInitialized) return;

    try {
      console.log("🌐 Khởi tạo SBot Global Manager...");

      // Wait for DOM to be ready
      if (document.readyState === "loading") {
        document.addEventListener("DOMContentLoaded", () =>
          this.createFloatingButton()
        );
      } else {
        this.createFloatingButton();
      }

      this.isInitialized = true;
      console.log("✅ SBot Global Manager đã khởi tạo thành công");
    } catch (error) {
      console.error("❌ Lỗi khởi tạo SBot Global Manager:", error);
    }
  }

  /**
   * Create floating button element
   */
  createFloatingButton() {
    // Remove existing button if any
    this.removeFloatingButton();

    // Create floating button
    const floatingBtn = document.createElement("div");
    floatingBtn.className = "sbotchat-global-floating-btn";
    floatingBtn.innerHTML = `
            <span class="sbot-text">SBOT</span>
            <div class="tooltip">Mở SBot Chat</div>
        `;

    // Add click event
    floatingBtn.addEventListener("click", () => this.openChat());

    // Add to body
    document.body.appendChild(floatingBtn);
    this.floatingButton = floatingBtn;

    console.log("🎯 Floating button đã được tạo");
  }

  /**
   * Remove floating button
   */
  removeFloatingButton() {
    const existingBtn = document.querySelector(".sbotchat-global-floating-btn");
    if (existingBtn && existingBtn.parentNode) {
      existingBtn.parentNode.removeChild(existingBtn);
    }
    this.floatingButton = null;
  }

  /**
   * Open chat interface
   */
  openChat() {
    try {
      console.log("🚀 Mở SBot Chat từ global manager...");

      // Hide floating button
      if (this.floatingButton) {
        this.floatingButton.style.display = "none";
      }

      // Try to use Odoo's action service
      if (window.odoo && window.odoo.define) {
        // Use Odoo's action system - check if __DEBUG__ exists
        const actionService =
          window.odoo.__DEBUG__ &&
          window.odoo.__DEBUG__.services &&
          window.odoo.__DEBUG__.services["action"];
        if (actionService) {
          actionService.doAction({
            type: "ir.actions.client",
            tag: "sbotchat.interface",
            name: "SBot Chat",
            target: "fullscreen",
          });
          return;
        }
      }

      // Fallback: direct navigation
      window.location.href = "/web#action=sbotchat.interface";
    } catch (error) {
      console.error("❌ Không thể mở SBot Chat:", error);
      // Fallback: try direct URL
      window.location.href = "/web#action=sbotchat.interface";
    }
  }

  /**
   * Show floating button
   */
  showFloatingButton() {
    if (this.floatingButton) {
      this.floatingButton.style.display = "flex";
    } else {
      this.createFloatingButton();
    }
  }

  /**
   * Hide floating button
   */
  hideFloatingButton() {
    if (this.floatingButton) {
      this.floatingButton.style.display = "none";
    }
  }
}

// Create global instance
const sbotchatGlobalManager = new SbotchatGlobalManager();

// Initialize when DOM is ready
if (document.readyState === "loading") {
  document.addEventListener("DOMContentLoaded", () =>
    sbotchatGlobalManager.init()
  );
} else {
  sbotchatGlobalManager.init();
}

// Export for use in other modules
window.sbotchatGlobalManager = sbotchatGlobalManager;

// Register floating button component
registry.category("actions").add("sbotchat.floating", SbotchatFloatingButton);

export { SbotchatFloatingButton, sbotchatGlobalManager };
