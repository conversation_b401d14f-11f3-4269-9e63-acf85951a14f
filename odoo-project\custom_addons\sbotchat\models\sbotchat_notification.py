# -*- coding: utf-8 -*-

from odoo import models, fields, api
import json
import logging
from datetime import datetime, timedelta

_logger = logging.getLogger(__name__)

class SbotchatNotification(models.Model):
    _name = 'sbotchat.notification'
    _description = 'SBot Chat Notifications'
    _order = 'create_date desc'
    _rec_name = 'title'

    # Basic fields
    title = fields.Char('Tiêu đề', required=True)
    message = fields.Text('Nội dung', required=True)
    notification_type = fields.Selection([
        ('info', 'Thông tin'),
        ('success', 'Thành công'),
        ('warning', 'Cảnh báo'),
        ('danger', 'Lỗi'),
        ('chat', 'Chat'),
        ('system', '<PERSON><PERSON> thống'),
        ('ai_generated', 'AI tạo')
    ], string='Loại thông báo', default='info', required=True)
    
    # Target users
    user_id = fields.Many2one('res.users', string='Người nhận cụ thể')
    target_type = fields.Selection([
        ('user', 'Người dùng cụ thể'),
        ('all_users', 'Tất cả người dùng'),
        ('conversation_participants', 'Thành viên cuộc trò chuyện'),
        ('role_based', 'Theo vai trò')
    ], string='Đối tượng nhận', default='user', required=True)
    
    # Related records
    conversation_id = fields.Many2one('sbotchat.conversation', string='Cuộc trò chuyện')
    message_id = fields.Many2one('sbotchat.message', string='Tin nhắn')
    
    # Status tracking
    is_read = fields.Boolean('Đã đọc', default=False)
    is_sent = fields.Boolean('Đã gửi', default=False)
    sent_date = fields.Datetime('Thời gian gửi')
    read_date = fields.Datetime('Thời gian đọc')
    
    # Bus notification
    bus_channel = fields.Char('Bus Channel')
    bus_message_type = fields.Char('Bus Message Type')
    bus_data = fields.Text('Bus Data (JSON)')
    
    # AI generated notifications
    created_by_ai = fields.Boolean('Tạo bởi AI', default=False)
    ai_context = fields.Text('Ngữ cảnh AI')
    ai_trigger = fields.Char('AI Trigger')
    
    # Auto cleanup
    expires_at = fields.Datetime('Hết hạn', help='Thông báo sẽ tự động xóa sau thời gian này')
    
    @api.model
    def create_notification(self, title, message, notification_type='info', 
                          target_type='user', user_id=None, conversation_id=None,
                          created_by_ai=False, ai_context=None, expires_hours=24):
        """Tạo thông báo và gửi realtime"""
        try:
            # Tính thời gian hết hạn
            expires_at = fields.Datetime.now() + timedelta(hours=expires_hours)
            
            # Tạo notification record
            notification = self.create({
                'title': title,
                'message': message,
                'notification_type': notification_type,
                'target_type': target_type,
                'user_id': user_id,
                'conversation_id': conversation_id,
                'created_by_ai': created_by_ai,
                'ai_context': ai_context,
                'expires_at': expires_at
            })
            
            # Gửi realtime notification
            notification.send_realtime_notification()
            
            return notification
            
        except Exception as e:
            _logger.error(f"Lỗi tạo notification: {str(e)}")
            return False
    
    def send_notification(self):
        """Gửi thông báo (alias cho send_realtime_notification)"""
        return self.send_realtime_notification()
        
    def send_realtime_notification(self):
        """Gửi thông báo realtime qua bus"""
        try:
            # Xác định channel và data
            if self.target_type == 'user' and self.user_id:
                channel = f'sbotchat_user_{self.user_id.id}'
            elif self.target_type == 'all_users':
                channel = 'sbotchat_global'
            elif self.target_type == 'conversation_participants' and self.conversation_id:
                channel = f'sbotchat_conversation_{self.conversation_id.id}'
            else:
                channel = 'sbotchat_global'
            
            # Tạo bus data
            bus_data = {
                'id': self.id,
                'title': self.title,
                'message': self.message,
                'type': self.notification_type,
                'conversation_id': self.conversation_id.id if self.conversation_id else None,
                'message_id': self.message_id.id if self.message_id else None,
                'created_by_ai': self.created_by_ai,
                'timestamp': self.create_date.isoformat(),
                'expires_at': self.expires_at.isoformat() if self.expires_at else None
            }
            
            # Gửi qua bus - Odoo 18.0 API
            if self.target_type == 'user' and self.user_id:
                # Sử dụng user._bus_send cho user cụ thể trong Odoo 18.0
                self.user_id._bus_send('sbotchat_notification', bus_data)
            else:
                # Sử dụng _sendone cho các channel khác
                self.env['bus.bus']._sendone(
                    channel,
                    'sbotchat_notification',
                    bus_data
                )
            
            # Cập nhật trạng thái
            self.write({
                'is_sent': True,
                'sent_date': fields.Datetime.now(),
                'bus_channel': channel,
                'bus_message_type': 'sbotchat_notification',
                'bus_data': json.dumps(bus_data)
            })
            
            _logger.info(f"Đã gửi notification {self.id} qua channel {channel}")
            
        except Exception as e:
            _logger.error(f"Lỗi gửi realtime notification {self.id}: {str(e)}")
    
    def mark_as_read(self):
        """Đánh dấu đã đọc"""
        self.write({
            'is_read': True,
            'read_date': fields.Datetime.now()
        })
    
    @api.model
    def cleanup_expired_notifications(self):
        """Dọn dẹp thông báo hết hạn"""
        try:
            expired_notifications = self.search([
                ('expires_at', '<', fields.Datetime.now()),
                ('is_read', '=', True)
            ])
            
            count = len(expired_notifications)
            if count > 0:
                expired_notifications.unlink()
                _logger.info(f"Đã xóa {count} thông báo hết hạn")
                
        except Exception as e:
            _logger.error(f"Lỗi cleanup notifications: {str(e)}")
    
    @api.model
    def optimize_performance(self):
        """Tối ưu hóa hiệu suất notification system"""
        try:
            from datetime import timedelta
            
            # Dọn dẹp thông báo cũ (>30 ngày) và đã đọc
            old_notifications = self.search([
                ('create_date', '<', fields.Datetime.now() - timedelta(days=30)),
                ('is_read', '=', True)
            ])
            if old_notifications:
                count = len(old_notifications)
                old_notifications.unlink()
                _logger.info(f"Cleaned up {count} old read notifications")
                
            # Tối ưu hóa bus channels - dọn dẹp bus records cũ
            try:
                old_bus_records = self.env['bus.bus'].search([
                    ('create_date', '<', fields.Datetime.now() - timedelta(hours=24))
                ])
                if old_bus_records:
                    bus_count = len(old_bus_records)
                    old_bus_records.unlink()
                    _logger.info(f"Cleaned up {bus_count} old bus records")
            except Exception as bus_error:
                _logger.warning(f"Could not clean bus records: {str(bus_error)}")
                
        except Exception as e:
            _logger.error(f"Error optimizing notification performance: {str(e)}")
    
    @api.model
    def get_user_notifications(self, user_id=None, limit=50, unread_only=False):
        """Lấy danh sách thông báo của user"""
        if not user_id:
            user_id = self.env.user.id
        
        domain = [
            '|',
            ('user_id', '=', user_id),
            ('target_type', '=', 'all_users')
        ]
        
        if unread_only:
            domain.append(('is_read', '=', False))
        
        notifications = self.search(domain, limit=limit)
        
        return [{
            'id': notif.id,
            'title': notif.title,
            'message': notif.message,
            'type': notif.notification_type,
            'is_read': notif.is_read,
            'created_by_ai': notif.created_by_ai,
            'conversation_id': notif.conversation_id.id if notif.conversation_id else None,
            'timestamp': notif.create_date.isoformat(),
            'expires_at': notif.expires_at.isoformat() if notif.expires_at else None
        } for notif in notifications]


class SbotchatNotificationManager(models.Model):
    """Manager cho việc tạo các loại thông báo khác nhau"""
    _name = 'sbotchat.notification.manager'
    _description = 'SBot Chat Notification Manager'
    
    @api.model
    def notify_new_message(self, conversation_id, sender_user_id, message_content):
        """Thông báo tin nhắn mới"""
        try:
            conversation = self.env['sbotchat.conversation'].browse(conversation_id)
            sender = self.env['res.users'].browse(sender_user_id)
            
            # Thông báo cho người tham gia cuộc trò chuyện (trừ người gửi)
            if conversation.user_id.id != sender_user_id:
                self.env['sbotchat.notification'].create_notification(
                    title=f'Tin nhắn mới từ {sender.name}',
                    message=f'Trong cuộc trò chuyện "{conversation.title}": {message_content[:100]}...',
                    notification_type='chat',
                    target_type='user',
                    user_id=conversation.user_id.id,
                    conversation_id=conversation_id
                )
            
        except Exception as e:
            _logger.error(f"Lỗi notify_new_message: {str(e)}")
    
    @api.model
    def notify_ai_response_ready(self, conversation_id, response_preview):
        """Thông báo AI đã trả lời"""
        try:
            conversation = self.env['sbotchat.conversation'].browse(conversation_id)
            
            self.env['sbotchat.notification'].create_notification(
                title='🤖 SBot đã trả lời',
                message=f'Trong cuộc trò chuyện "{conversation.title}": {response_preview[:100]}...',
                notification_type='success',
                target_type='user',
                user_id=conversation.user_id.id,
                conversation_id=conversation_id,
                created_by_ai=True
            )
            
        except Exception as e:
            _logger.error(f"Lỗi notify_ai_response_ready: {str(e)}")
    
    @api.model
    def notify_ai_processing(self, conversation_id, message):
        """Thông báo AI đang xử lý"""
        try:
            conversation = self.env['sbotchat.conversation'].browse(conversation_id)
            
            self.env['sbotchat.notification'].create_notification(
                title='🔄 SBot đang xử lý...',
                message=message,
                notification_type='info',
                target_type='user',
                user_id=conversation.user_id.id,
                conversation_id=conversation_id,
                created_by_ai=True,
                expires_hours=1  # Hết hạn nhanh hơn
            )
            
        except Exception as e:
            _logger.error(f"Lỗi notify_ai_processing: {str(e)}")
    
    @api.model
    def notify_system_update(self, title, message, target_users='all'):
        """Thông báo cập nhật hệ thống"""
        try:
            if target_users == 'all':
                self.env['sbotchat.notification'].create_notification(
                    title=title,
                    message=message,
                    notification_type='system',
                    target_type='all_users'
                )
            else:
                for user_id in target_users:
                    self.env['sbotchat.notification'].create_notification(
                        title=title,
                        message=message,
                        notification_type='system',
                        target_type='user',
                        user_id=user_id
                    )
            
        except Exception as e:
            _logger.error(f"Lỗi notify_system_update: {str(e)}")
    
    @api.model
    def ai_create_notification(self, title, message, notification_type='ai_generated', 
                             target_user_id=None, context=None, trigger=None):
        """Cho phép AI tạo thông báo tùy chỉnh"""
        try:
            notification = self.env['sbotchat.notification'].create_notification(
                title=title,
                message=message,
                notification_type=notification_type,
                target_type='user' if target_user_id else 'all_users',
                user_id=target_user_id,
                created_by_ai=True,
                ai_context=context,
                expires_hours=24
            )
            
            # Log AI notification creation
            _logger.info(f"AI tạo thông báo: {title} - Target: {target_user_id or 'all'}")
            
            return {
                'success': True,
                'notification_id': notification.id,
                'message': f'Đã tạo thông báo "{title}" thành công'
            }
            
        except Exception as e:
            _logger.error(f"Lỗi AI tạo notification: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            } 